const { Client, GatewayIntentBits, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// image processing
const {
  processScreenshots,
  combineResults,
  createResultsEmbed,
  checkCooldown
} = require('./imageProcessor');

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

client.once('ready', () => {
  console.log(`Logged in as ${client.user.tag}`);
});

client.on('messageCreate', async message => {
  if (message.author.bot) return;

  if (message.channel.id === process.env.CHANNEL_ID) {


    var attachments;
    if (!message.attachments.size) {
      // const noAttachmentsEmbed = new EmbedBuilder()
      //   .setDescription('**No attachments found.** Please attach game screenshots when using the `!results` command.')
      //   .setColor('Red');
      // message.reply({ embeds: [noAttachmentsEmbed] });

      if (!message.messageSnapshots.first()) return;
      if (!message.messageSnapshots.first().attachments.size) return;
      attachments = message.messageSnapshots.first().attachments;
    } else {
      attachments = message.attachments;
    }

    //too many attachments
    if (attachments.size > process.env.MAX_ATTACHMENTS) {
      const tooManyAttachmentsEmbed = new EmbedBuilder()
        .setDescription(`**Too many images.** Please limit your results to \`${process.env.MAX_ATTACHMENTS}\` screenshots at a time.`)
        .setColor('Red');
      message.reply({ embeds: [tooManyAttachmentsEmbed] });
      return;
    }

    // Check cooldown
    const cooldownResult = checkCooldown(message.author.id);
    if (cooldownResult.onCooldown) return message.react('⏳');

    const loadingEmbed = new EmbedBuilder()
      .setDescription(`**Scanning images... \`${attachments.size}\`**`)
      .setColor('Blue');
    const responseMsg = await message.reply({ embeds: [loadingEmbed] });

    try {
      // Process and validate all images
      const screenshotResults = await processScreenshots(attachments);

      // Check if there was an error during processing
      if (screenshotResults.error) {
        let errorMessage = screenshotResults.error;
        if (screenshotResults.error === "Contains image that is not a result") {
          errorMessage = `**Contains image that is not a result**\n${screenshotResults.invalidImageReason || 'One or more images are not valid game screenshots.'}`;
        }

        const errorEmbed = new EmbedBuilder()
          .setDescription(errorMessage)
          .setColor('Red');

        await responseMsg.edit({ embeds: [errorEmbed] });
        return;
      }

      // Combine the results
      const combinedResults = combineResults(screenshotResults);

      const resultsEmbed = createResultsEmbed(combinedResults);
      await responseMsg.edit({ embeds: [resultsEmbed] });

    } catch (error) {
      console.error('Error processing images:', error);

      const errorEmbed = new EmbedBuilder()
        .setDescription(`**Error processing images:** ${error.message}`)
        .setColor('Red');

      await responseMsg.edit({ embeds: [errorEmbed] });
    }
  }
});

client.login(process.env.TOKEN);
