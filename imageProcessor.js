const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const sharp = require('sharp');
const { EmbedBuilder } = require('discord.js');

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const userCooldowns = new Map();

function checkCooldown(userId) {
  const cooldownSeconds = parseInt(process.env.COOLDOWN_SECONDS) || 60; // Default 60 seconds if not set
  const now = Date.now();
  const lastUse = userCooldowns.get(userId) || 0;
  const timePassed = (now - lastUse) / 1000; // Convert to seconds

  if (timePassed < cooldownSeconds) {
    return {
      onCooldown: true,
      remainingSeconds: Math.ceil(cooldownSeconds - timePassed)
    };
  }

  userCooldowns.set(userId, now);
  return {
    onCooldown: false,
    remainingSeconds: 0
  };
}

// Merge multiple images into a single image
async function mergeImages(imageBuffers) {
  if (imageBuffers.length === 1) {
    return imageBuffers[0];
  }

  try {
    // Get dimensions of all images
    const imageInfos = await Promise.all(
      imageBuffers.map(async (buffer) => {
        const metadata = await sharp(buffer).metadata();
        return { buffer, width: metadata.width, height: metadata.height };
      })
    );

    // Calculate grid dimensions (try to make it roughly square)
    const totalImages = imageInfos.length;
    const cols = Math.ceil(Math.sqrt(totalImages));
    const rows = Math.ceil(totalImages / cols);

    // Find the maximum width and height to standardize image sizes
    const maxWidth = Math.max(...imageInfos.map(info => info.width));
    const maxHeight = Math.max(...imageInfos.map(info => info.height));

    // Resize all images to the same size
    const resizedImages = await Promise.all(
      imageInfos.map(async (info) => {
        return await sharp(info.buffer)
          .resize(maxWidth, maxHeight, { fit: 'contain', background: { r: 0, g: 0, b: 0, alpha: 1 } })
          .toBuffer();
      })
    );

    // Calculate final canvas size
    const canvasWidth = cols * maxWidth;
    const canvasHeight = rows * maxHeight;

    // Create the merged image
    const mergedImage = sharp({
      create: {
        width: canvasWidth,
        height: canvasHeight,
        channels: 4,
        background: { r: 0, g: 0, b: 0, alpha: 1 }
      }
    });

    // Create composite array for positioning images
    const composite = [];
    for (let i = 0; i < resizedImages.length; i++) {
      const row = Math.floor(i / cols);
      const col = i % cols;
      composite.push({
        input: resizedImages[i],
        left: col * maxWidth,
        top: row * maxHeight
      });
    }

    const result = await mergedImage.composite(composite).png().toBuffer();
    console.log(`Successfully merged ${imageBuffers.length} images into a ${canvasWidth}x${canvasHeight} grid`);
    return result;

  } catch (error) {
    console.error('Error merging images:', error);
    // Fallback to first image if merging fails
    return imageBuffers[0];
  }
}

// Process screenshots by merging them into one image first
async function processScreenshots(attachments) {
  const screenshotsDir = path.join(process.cwd(), 'screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir);
  }

  const imageBuffers = [];
  const savedImagePaths = [];

  // Download all images first
  for (const [id, attachment] of attachments) {
    try {
      console.log(`Downloading attachment ${id}...`);

      // Download image
      const response = await axios.get(attachment.url, { responseType: 'arraybuffer' });
      const imageBuffer = Buffer.from(response.data);

      // Save image locally for cleanup later
      const imagePath = path.join(screenshotsDir, `screenshot_${Date.now()}_${id}.png`);
      fs.writeFileSync(imagePath, imageBuffer);
      savedImagePaths.push(imagePath);

      imageBuffers.push(imageBuffer);
    } catch (error) {
      console.error(`Error downloading attachment ${id}:`, error);
    }
  }

  if (imageBuffers.length === 0) {
    cleanupScreenshots(savedImagePaths);
    return [];
  }

  try {
    // Merge all images into one
    console.log(`Merging ${imageBuffers.length} images...`);
    const mergedImageBuffer = await mergeImages(imageBuffers);

    // Save merged image for debugging (optional)
    const mergedImagePath = path.join(screenshotsDir, `merged_${Date.now()}.png`);
    fs.writeFileSync(mergedImagePath, mergedImageBuffer);
    savedImagePaths.push(mergedImagePath);

    // Process the merged image
    const base64Image = mergedImageBuffer.toString('base64');
    const imageResult = await processOneImage(base64Image);

    // Clean up all saved images
    cleanupScreenshots(savedImagePaths);

    if (imageResult && imageResult.players && imageResult.players.length > 0) {
      console.log(`Merged image processing successful: found ${imageResult.players.length} players`);
      return [imageResult]; // Return as array to maintain compatibility
    } else {
      console.log('Merged image processing failed or no valid results found', imageResult);
      return [];
    }

  } catch (error) {
    console.error('Error processing merged image:', error);
    cleanupScreenshots(savedImagePaths);
    return [];
  }
}

// cleanup screenshots
function cleanupScreenshots(filePaths) {
  if (!filePaths || filePaths.length === 0) return;

  filePaths.forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error(`Failed to delete file ${filePath}:`, error.message);
    }
  });
}

// clean username
function cleanUsername(username) {
  if (!username) return '';

  let cleaned = username
    .replace(/卍/g, "")
    .replace(/卐/g, "")
    .replace(/!/g, "I")
    .replace(/\|/g, "I")
    .replace(/\l/g, "I");

  cleaned = cleaned.replace(/(.)\1{9,}/g, function(_match, char) {
    return char.repeat(9);
  });

  return cleaned;
}

//send to gpt
async function processOneImage(base64Image) {
  try {
    const messages = [
      {
        role: "system",
        content: "You are an image data extractor that processes merged game screenshots. This image may contain multiple game scoreboards arranged in a grid format, along with other non-game content that should be IGNORED. Your task: 1) Scan the entire image for sections that contain BOTH the words \"DMG\" and \"EDA\" in a scoreboard TABLE format. 2) IGNORE any sections that don't have both \"DMG\" and \"EDA\" columns - these are not game results. 3) For each valid scoreboard section found, extract player data. 4) Combine all players from all valid scoreboards into one response. For each player in valid scoreboards: extract username (not @username), team_color, and EDA values. ⚠️ Team color determination: look ONLY at the background color behind the ping icon (small bar at far-right of player row). GOLD/YELLOW background = gold team, PURPLE/VIOLET background = purple team. ❌ IGNORE player row position, list order, or colors behind name/stats. Players are NOT grouped by team and scoreboard order is misleading. Trust ONLY the ping background color. Extract exact EDA numbers (kills assists deaths) like: '5 3 2'. If multiple scoreboards are present, process ALL of them and combine the results."
      },
      {
        role: "user",
        content: [
          {
            type: "text",
            text: "This image may contain multiple game scoreboards in a grid layout, plus other content to ignore. Scan the entire image and: 1) Find all sections with BOTH \"DMG\" and \"EDA\" columns in table format. 2) IGNORE any sections without both columns. 3) For each valid scoreboard, extract all players with: name, team_color (purple/gold based on ping background color only), and EDA values (3 numbers separated by spaces). 4) Combine ALL players from ALL valid scoreboards into one JSON response with a 'players' array. Remember: team_color is determined ONLY by ping background color, NOT by player position or grouping. Process all valid scoreboards found in the image."
          },
          {
            type: "image_url",
            image_url: {
              url: `data:image/png;base64,${base64Image}`
            }
          }
        ]
      }
    ];

    console.log('Sending request to OpenAI...');
    const chatCompletion = await openai.chat.completions.create({
      model: "gpt-4.1",
      messages: messages,
      response_format: { type: "json_object" },
      max_tokens: 1000
    });

    const response = JSON.parse(chatCompletion.choices[0].message.content);

    if (!response.players || !Array.isArray(response.players)) {
      console.log('Invalid response format, missing players array:', response);
      if (response.data && Array.isArray(response.data)) {
        response.players = response.data;
      } else {
        return { players: [] };
      }
    }

    // Process the EDA values to kills and deaths
    const processedPlayers = response.players.map(player => {
      let kills = 0;
      let deaths = 0;

      if (player.EDA || player.eda) {
        const edaString = (player.EDA || player.eda).toString().trim();
        const edaValues = edaString.split(/\s+/);

        if (edaValues.length >= 2) {
          kills = parseInt(edaValues[0]) || 0;
          deaths = parseInt(edaValues[1]) || 0;
          console.log(`Player ${player.name}: EDA "${edaString}" → Kills: ${kills}, Deaths: ${deaths}`);
        }
      }

      return {
        name: cleanUsername(player.name),
        team_color: player.team_color,
        kills: kills,
        deaths: deaths
      };
    });

    return { players: processedPlayers };
  } catch (error) {
    console.error('Error in processOneImage:', error);
    return { players: [] };
  }
}

//combine results
function combineResults(screenshotResults) {
  if (!screenshotResults || screenshotResults.length === 0) {
    return {
      error: "No valid game results found in the provided screenshots."
    };
  }

  const playerStats = new Map();
  const nameVariants = new Map();

  // Process all screenshots
  screenshotResults.forEach(result => {
    if (result.players) {
      result.players.forEach(player => {
        const name = cleanUsername(player.name);
        const teamColor = player.team_color || '';
        const kills = player.kills || 0;
        const deaths = player.deaths || 0;

        if (!name || !teamColor) {
          console.log(`Skipping player with incomplete data:`, player);
          return;
        }

        // check for variants
        let matchedName = findSimilarName(name, Array.from(playerStats.keys()));

        if (matchedName && matchedName !== name) {
          console.log(`Matched "${name}" as a variant of "${matchedName}"`);

          if (!nameVariants.has(matchedName)) {
            nameVariants.set(matchedName, []);
          }
          nameVariants.get(matchedName).push(name);

          // Use the matched name for stats
          const existing = playerStats.get(matchedName);
          existing.kills += kills;
          existing.deaths += deaths;
          existing.rounds += 1;

          // Track team colors
          if (!existing.teamColors.includes(teamColor.toLowerCase())) {
            existing.teamColors.push(teamColor.toLowerCase());
          }

        } else {
          const actualName = matchedName || name;

          const existing = playerStats.get(actualName) || {
            name: actualName,
            teamColor: teamColor,
            kills: 0,
            deaths: 0,
            rounds: 0,
            teamColors: []
          };

          existing.kills += kills;
          existing.deaths += deaths;
          existing.rounds += 1;

          // Track team colors
          if (!existing.teamColors.includes(teamColor.toLowerCase())) {
            existing.teamColors.push(teamColor.toLowerCase());
          }

          playerStats.set(actualName, existing);

        }
      });
    }
  });


  const purpleTeam = [];
  const goldTeam = [];

  let teamSwitchCount = 0;

  // Sort players into teams
  playerStats.forEach(player => {
    // Check for multiple teams
    const isPurple = player.teamColors.some(color => color.includes('purple'));
    const isGold = player.teamColors.some(color => color.includes('gold') || color.includes('yellow'));
    const teamSwitcher = isPurple && isGold;
    player.teamSwitcher = teamSwitcher;
    if (teamSwitcher) teamSwitchCount++;

    // Add rounds played to player data
    const gamesPlayed = player.rounds;

    // Group player by their last known team
    const color = player.teamColor.toLowerCase();
    if (color.includes('purple')) {
      purpleTeam.push({
        name: player.name,
        kills: player.kills,
        deaths: player.deaths,
        teamSwitcher: teamSwitcher,
        gamesPlayed: gamesPlayed,
        totalGames: screenshotResults.length
      });
    } else if (color.includes('gold') || color.includes('yellow')) {
      goldTeam.push({
        name: player.name,
        kills: player.kills,
        deaths: player.deaths,
        teamSwitcher: teamSwitcher,
        gamesPlayed: gamesPlayed,
        totalGames: screenshotResults.length
      });
    } else {
      console.log(`Player ${player.name} has unknown team color: ${player.teamColor}`);
    }
  });

  console.log(`Purple team has ${purpleTeam.length} players, Gold team has ${goldTeam.length} players`);

  return {
    title: `Match Results (from ${screenshotResults.length} rounds)`,
    teams: [
      {
        name: "Purple Team",
        players: purpleTeam
      },
      {
        name: "Gold Team",
        players: goldTeam
      }
    ],
    teamSwitchCount: teamSwitchCount
  };
}

// Update findSimilarName to better handle repetitive characters
function findSimilarName(name, existingNames) {
  if (existingNames.includes(name)) {
    return name;
  }

  const nameLower = name.toLowerCase();

  // Case-insensitive match
  const caseInsensitiveMatch = existingNames.find(n => n.toLowerCase() === nameLower);
  if (caseInsensitiveMatch) {
    return caseInsensitiveMatch;
  }


  const simplifyName = (str) => str.toLowerCase().replace(/(.)\1{2,}/g, '$1$1');
  const simplifiedName = simplifyName(name);

  const patternMatch = existingNames.find(existingName => {
    return simplifyName(existingName) === simplifiedName;
  });

  if (patternMatch) {
    return name.length <= patternMatch.length ? name : patternMatch;
  }

  // Regular similarity checks (existing code)
  const similarNameMatch = existingNames.find(existingName => {
    // If one is a subset of the other
    if (existingName.toLowerCase().includes(nameLower) ||
        nameLower.includes(existingName.toLowerCase())) {
      return true;
    }

    const distance = levenshteinDistance(nameLower, existingName.toLowerCase());

    // If names are short, allow 1 character difference
    // For longer names, allow up to 2 character differences
    const threshold = Math.min(existingName.length, name.length) <= 5 ? 1 : 2;

    return distance <= threshold;
  });

  // When we have a match, prefer the shorter version
  if (similarNameMatch) {
    return name.length <= similarNameMatch.length ? name : similarNameMatch;
  }

  return null;
}

function levenshteinDistance(a, b) {
  if (a.length === 0) return b.length;
  if (b.length === 0) return a.length;

  const matrix = [];

  for (let i = 0; i <= b.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= a.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= b.length; i++) {
    for (let j = 1; j <= a.length; j++) {
      if (b.charAt(i - 1) === a.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[b.length][a.length];
}

//final embed
function createResultsEmbed(resultsData) {
  const embed = new EmbedBuilder()
    .setColor('Green')
    .setTimestamp();

  if (typeof resultsData === 'string') {
    embed.setTitle('Error Processing Results');
    embed.setDescription(`Unable to process response format:\n\`\`\`\n${resultsData.substring(0, 1000)}\n\`\`\``);
    embed.setColor('Orange');
    return embed;
  }

  if (resultsData.error) {
    embed.setTitle('Error Processing Results');
    embed.setDescription(`**Error:** ${resultsData.error}`);
    embed.setColor('Red');
    return embed;
  }

  try {
    embed.setTitle(resultsData.title || "Match Results");

    let description = '';

    if (resultsData.teams && resultsData.teams.length > 0) {
      // Find the team with the player that has the most kills
      let topPlayerGlobal = null;
      let topPlayerTeamIndex = -1;

      // Find the player with most kills overall
      resultsData.teams.forEach((team, teamIndex) => {
        if (team.players && team.players.length > 0) {
          team.players.forEach(player => {
            if (player.kills !== undefined) {
              if (!topPlayerGlobal || player.kills > topPlayerGlobal.kills) {
                topPlayerGlobal = player;
                topPlayerTeamIndex = teamIndex;
              }
            }
          });
        }
      });

      // Find player with most kills on opposite team
      let secondTopPlayer = null;

      if (resultsData.teams.length > 1 && topPlayerTeamIndex !== -1) {
        for (let i = 0; i < resultsData.teams.length; i++) {
          if (i === topPlayerTeamIndex) continue;

          const team = resultsData.teams[i];
          if (team.players && team.players.length > 0) {
            team.players.forEach(player => {
              if (player.kills !== undefined) {
                if (!secondTopPlayer || player.kills > secondTopPlayer.kills) {
                  secondTopPlayer = player;
                }
              }
            });
          }
        }
      }

      // highlights
      if (topPlayerGlobal) {
        topPlayerGlobal.highlight = '👑';
      }

      if (secondTopPlayer) {
        secondTopPlayer.highlight = '🥈';
      }

      resultsData.teams.forEach(team => {
        if (!team.players || team.players.length === 0) return;

        description += `**${team.name}:**\n`;

        const sortedPlayers = [...team.players].sort((a, b) => (b.kills || 0) - (a.kills || 0));

        sortedPlayers.forEach(player => {
          let playerLine = '';

          if (player.highlight === '👑') {
            playerLine = `**${player.name}**: `;
          } else {
            playerLine = `${player.name}: `;
          }

          // kills/deaths
          playerLine += `${player.kills}/${player.deaths}`;

          // Add KD ratio
          if (player.deaths > 0) {
            const kd = (player.kills / player.deaths).toFixed(2);
            playerLine += ` (${kd} KD)`;
          } else if (player.kills > 0) {
            playerLine += ` (${player.kills} KD)`;
          } else {
            playerLine += ` (0.00 KD)`;
          }

          // Add games played if not all games
          if (player.gamesPlayed < player.totalGames) {
            playerLine += ` (played ${player.gamesPlayed}/${player.totalGames})`;
          }

          // emoji
          if (player.highlight) {
            playerLine += ` ${player.highlight}`;
          }

          // on multiple teams - only show if 2 or fewer players switched
          if (player.teamSwitcher && resultsData.teamSwitchCount <= 2) {
            playerLine += ` \`🔁\``;
          }

          description += `${playerLine}\n`;
        });

        description += '\n';
      });
    }

    embed.setDescription(description || "No player data found in the response.");

    if (resultsData.teamSwitchCount > 2) {
      embed.setFooter({ text: 'Teams may have switched colors during these matches' });
    }

  } catch (error) {
    console.error('Error formatting embed:', error);
    embed.setTitle('Error Formatting Results');
    embed.setDescription(`Error formatting game results:\n\`\`\`\n${error.message}\n\`\`\``);
    embed.setColor('Red');
  }

  return embed;
}

module.exports = {
  processScreenshots,
  mergeImages,
  cleanupScreenshots,
  processOneImage,
  combineResults,
  findSimilarName,
  levenshteinDistance,
  createResultsEmbed,
  checkCooldown,
  cleanUsername
};